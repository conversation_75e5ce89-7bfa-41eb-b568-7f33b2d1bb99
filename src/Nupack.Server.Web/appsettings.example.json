{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "NuGetServer": {
    "BaseUrl": "https://your-nuget-server.com",
    "Name": "Your Company NuGet"
  },
  "Branding": {
    "CompanyName": "Your Company Name",
    "NugetSourceUrl": "https://your-nuget-server.com/v3/index.json"
  }
}

// Example configurations for different deployment scenarios:

// Corporate Internal Server
/*
{
  "Branding": {
    "CompanyName": "Acme Corporation",
    "NugetSourceUrl": "https://nuget.internal.acme.com/v3/index.json"
  }
}
*/

// Cloud-Hosted Public Server
/*
{
  "Branding": {
    "CompanyName": "Open Source Project",
    "NugetSourceUrl": "https://packages.myproject.org/v3/index.json"
  }
}
*/

// Development Team Server
/*
{
  "Branding": {
    "CompanyName": "DevTeam Alpha",
    "NugetSourceUrl": "https://nuget-dev.company.com/v3/index.json"
  }
}
*/
